{"name": "AsyncTCP-esphome", "description": "Asynchronous TCP Library for ESP32", "keywords": "async,tcp", "authors": {"name": "<PERSON><PERSON>", "maintainer": true}, "repository": {"type": "git", "url": "https://github.com/esphome/AsyncTCP.git"}, "version": "2.1.4", "license": "LGPL-3.0", "frameworks": "a<PERSON><PERSON><PERSON>", "platforms": ["espressif32", "libretiny"], "build": {"libCompatMode": 2}}