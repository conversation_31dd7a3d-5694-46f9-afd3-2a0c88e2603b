name: Publish

on:
  release:
    types: [published]

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.2.1
      - name: Set up Python
        uses: actions/setup-python@v5.2.0
        with:
          python-version: '3.x'
      - name: Install platformio
        run: pip install --upgrade platformio
      - name: Publish package
        env:
          PLATFORMIO_AUTH_TOKEN: ${{ secrets.PLATFORMIO_AUTH_TOKEN }}
        run: pio package publish --owner esphome --non-interactive
