{"name": "Nova Fitness Sds dust sensors library", "version": "1.5.1", "keywords": "sds011, sds, sds021, dust, sensor, pm10, pm25, arduino, esp8266, esp32", "description": "A high-level abstaction over Sds sensors family", "homepage": "https://github.com/lewapek/sds-dust-sensors-arduino-library", "license": "MIT", "authors": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/lewapek", "maintainer": true}, "repository": {"type": "git", "url": "https://github.com/lewapek/sds-dust-sensors-arduino-library", "branch": "master"}, "frameworks": "a<PERSON><PERSON><PERSON>", "platforms": ["atmelavr", "espressif8266", "espressif32", "atmelsam"]}