name: Async TCP CI

on:
  push:
    branches:
    - master
    - release/*
  pull_request:

jobs:

  build-arduino:
    name: Ard<PERSON><PERSON> on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macOS-latest]
    steps:
    - uses: actions/checkout@v1
    - name: Build Tests
      run: bash ./.github/scripts/on-push.sh 0 1

  build-pio:
    name: PlatformIO on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macOS-latest]
    steps:
    - uses: actions/checkout@v1
    - name: Build Tests
      run: bash ./.github/scripts/on-push.sh 1 1
