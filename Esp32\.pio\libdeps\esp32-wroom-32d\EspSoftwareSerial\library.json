{"name": "EspSoftwareSerial", "version": "8.2.0", "description": "Implementation of the Arduino software serial for ESP8266/ESP32.", "keywords": ["serial", "io", "softwareserial"], "repository": {"type": "git", "url": "https://github.com/plerup/espsoftwareserial"}, "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "license": "LGPL-2.1+", "frameworks": "a<PERSON><PERSON><PERSON>", "platforms": ["espressif8266", "espressif32"], "dependencies": {"dok-net/ghostl": "^1.0.0"}}