; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-wroom-32d]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 9600
lib_deps = 
	me-no-dev/AsyncTCP@^1.1.1
	esphome/ESPAsyncWebServer-esphome@^3.3.0
	ricki-z/SDS011 sensor Library@^0.0.8
	lewapek/Nova Fitness Sds dust sensors library@^1.5.1
	adafruit/DHT sensor library@^1.4.6
	knolleary/PubSubClient@^2.8
	plerup/EspSoftwareSerial@^8.2.0
	mikalhart/TinyGPSPlus@^1.1.0
