{"name": "ESPAsyncWebServer-esphome", "description": "Asynchronous HTTP and WebSocket Server Library for ESP8266 and ESP32", "keywords": "http,async,websocket,webserver", "authors": {"name": "ESPHome Team", "maintainer": true}, "repository": {"type": "git", "url": "https://github.com/esphome/ESPAsyncWebServer.git"}, "version": "3.4.0", "license": "LGPL-3.0", "frameworks": "a<PERSON><PERSON><PERSON>", "platforms": ["espressif8266", "espressif32", "libretiny"], "dependencies": [{"owner": "esphome", "name": "ESPAsyncTCP-esphome", "platforms": "espressif8266"}, {"owner": "esphome", "name": "AsyncTCP-esphome", "platforms": ["espressif32", "libretiny"]}, {"name": "Hash", "platforms": "espressif8266"}, {"name": "ESP8266WiFi", "platforms": "espressif8266"}]}