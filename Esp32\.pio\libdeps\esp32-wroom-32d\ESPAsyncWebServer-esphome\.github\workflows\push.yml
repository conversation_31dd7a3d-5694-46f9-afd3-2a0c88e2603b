name: ESP Async Web Server CI

on:
  push:
    branches:
    - master
    - release/*
  pull_request:

jobs:

  build-arduino:
    name: <PERSON><PERSON><PERSON><PERSON> for ${{ matrix.board }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macOS-latest]
        board: [esp32, esp8266]
    steps:
    - uses: actions/checkout@v3.0.2
    - name: Build Tests
      run: bash ./.github/scripts/on-push.sh ${{ matrix.board }} 0 1

  build-pio:
    name: PlatformIO for ${{ matrix.board }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macOS-latest]
        board: [esp32, esp8266]
    steps:
    - uses: actions/checkout@v3.0.2
    - name: Build Tests
      run: bash ./.github/scripts/on-push.sh ${{ matrix.board }} 1 1
